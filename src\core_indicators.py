"""
Consolidated Technical Indicators Module for TTM Squeeze Trading System
Combines all technical indicators and TTM Squeeze logic
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

# ============================================================================
# TTM SQUEEZE DATA STRUCTURES
# ============================================================================

@dataclass
class SqueezeSignal:
    """Data class for TTM Squeeze signals"""
    timestamp: pd.Timestamp
    symbol: str
    timeframe: str
    is_squeeze: bool
    momentum: float
    momentum_color: str  # 'red', 'yellow', 'green'
    bb_upper: float
    bb_lower: float
    kc_upper: float
    kc_lower: float
    signal_strength: float
    entry_signal: bool

# ============================================================================
# CORE TECHNICAL INDICATORS
# ============================================================================

class TechnicalIndicators:
    """Core technical indicators for trading analysis"""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """Simple Moving Average"""
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """Exponential Moving Average"""
        return data.ewm(span=period).mean()
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Bollinger Bands"""
        sma = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        
        return upper_band, sma, lower_band
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Average True Range"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return true_range.rolling(window=period).mean()
    
    @staticmethod
    def keltner_channels(high: pd.Series, low: pd.Series, close: pd.Series, 
                        period: int = 20, atr_multiplier: float = 1.5) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Keltner Channels"""
        ema_close = TechnicalIndicators.ema(close, period)
        atr_values = TechnicalIndicators.atr(high, low, close, period)
        
        upper_channel = ema_close + (atr_values * atr_multiplier)
        lower_channel = ema_close - (atr_values * atr_multiplier)
        
        return upper_channel, ema_close, lower_channel
    
    @staticmethod
    def momentum(close: pd.Series, period: int = 12) -> pd.Series:
        """Price Momentum"""
        return close - close.shift(period)
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """Relative Strength Index"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD (Moving Average Convergence Divergence)"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                   k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """Stochastic Oscillator"""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return k_percent, d_percent

# ============================================================================
# TTM SQUEEZE INDICATOR
# ============================================================================

class TTMSqueezeIndicator:
    """TTM Squeeze indicator implementation"""
    
    def __init__(self, bb_period: int = 20, bb_std_dev: float = 2.0,
                 kc_period: int = 20, kc_atr_multiplier: float = 1.5,
                 momentum_period: int = 12):
        self.bb_period = bb_period
        self.bb_std_dev = bb_std_dev
        self.kc_period = kc_period
        self.kc_atr_multiplier = kc_atr_multiplier
        self.momentum_period = momentum_period
        
        logger.debug(f"TTM Squeeze initialized: BB({bb_period}, {bb_std_dev}), KC({kc_period}, {kc_atr_multiplier}), Mom({momentum_period})")
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate TTM Squeeze indicator"""
        try:
            if len(data) < max(self.bb_period, self.kc_period, self.momentum_period) + 10:
                logger.warning(f"Insufficient data for TTM Squeeze calculation: {len(data)} bars")
                return None
            
            df = data.copy()
            
            # Calculate Bollinger Bands
            bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(
                df['close'], self.bb_period, self.bb_std_dev
            )
            
            # Calculate Keltner Channels
            kc_upper, kc_middle, kc_lower = TechnicalIndicators.keltner_channels(
                df['high'], df['low'], df['close'], 
                self.kc_period, self.kc_atr_multiplier
            )
            
            # TTM Squeeze condition: Bollinger Bands inside Keltner Channels
            squeeze_on = (bb_upper <= kc_upper) & (bb_lower >= kc_lower)
            
            # Calculate momentum
            highest_high = df['high'].rolling(window=self.momentum_period).max()
            lowest_low = df['low'].rolling(window=self.momentum_period).min()
            avg_hl = (highest_high + lowest_low) / 2
            avg_close = TechnicalIndicators.sma(df['close'], self.momentum_period)
            momentum = df['close'] - avg_hl.shift(int(self.momentum_period / 2))
            
            # Add TTM Squeeze data to dataframe
            df['bb_upper'] = bb_upper
            df['bb_middle'] = bb_middle
            df['bb_lower'] = bb_lower
            df['kc_upper'] = kc_upper
            df['kc_middle'] = kc_middle
            df['kc_lower'] = kc_lower
            df['is_squeeze'] = squeeze_on
            df['momentum'] = momentum
            
            # Calculate momentum color (for visualization)
            df['momentum_color'] = 'gray'
            df.loc[momentum > 0, 'momentum_color'] = 'lime'
            df.loc[momentum < 0, 'momentum_color'] = 'red'
            
            # Detect squeeze release
            df['squeeze_release'] = (~squeeze_on) & squeeze_on.shift(1)
            
            # Signal strength
            df['signal_strength'] = self._calculate_signal_strength(df)
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating TTM Squeeze: {e}")
            return None
    
    def _calculate_signal_strength(self, df: pd.DataFrame) -> pd.Series:
        """Calculate signal strength based on multiple factors"""
        try:
            strength = pd.Series(0.5, index=df.index)  # Base strength
            
            # Momentum strength
            momentum_abs = abs(df['momentum'])
            momentum_max = momentum_abs.rolling(window=20).max()
            momentum_strength = momentum_abs / momentum_max.replace(0, 1)
            strength += momentum_strength * 0.3
            
            # Squeeze duration (longer squeeze = stronger release)
            squeeze_duration = df['is_squeeze'].rolling(window=20).sum()
            duration_strength = np.minimum(squeeze_duration / 10, 1.0)  # Cap at 1.0
            strength += duration_strength * 0.2
            
            # Volume confirmation (if available)
            if 'volume' in df.columns:
                volume_avg = df['volume'].rolling(window=20).mean()
                volume_strength = np.minimum(df['volume'] / volume_avg, 2.0) / 2.0
                strength += volume_strength * 0.1
            
            # Normalize to 0-1 range
            strength = np.clip(strength, 0.0, 1.0)
            
            return strength
            
        except Exception as e:
            logger.error(f"Error calculating signal strength: {e}")
            return pd.Series(0.5, index=df.index)
    
    def detect_signals(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect TTM Squeeze signals"""
        try:
            if data is None or len(data) < 2:
                return []
            
            signals = []
            
            # Get the latest few bars for signal detection
            recent_data = data.tail(5)
            
            for i in range(1, len(recent_data)):
                current = recent_data.iloc[i]
                previous = recent_data.iloc[i-1]
                
                # Squeeze release signal
                if current['squeeze_release']:
                    signal_type = "TTM Squeeze release"
                    
                    # Determine direction based on momentum
                    if current['momentum'] > 0:
                        direction = "bullish"
                        signal_type += " (bullish)"
                    else:
                        direction = "bearish"
                        signal_type += " (bearish)"
                    
                    signals.append({
                        'signal_type': signal_type,
                        'direction': direction,
                        'timestamp': current.get('timestamp', current.name),
                        'price': current['close'],
                        'momentum': current['momentum'],
                        'signal_strength': current['signal_strength'],
                        'squeeze_duration': previous.get('is_squeeze', False)
                    })
                
                # Squeeze setup signal (entering squeeze)
                elif current['is_squeeze'] and not previous['is_squeeze']:
                    signals.append({
                        'signal_type': "TTM Squeeze setup",
                        'direction': "neutral",
                        'timestamp': current.get('timestamp', current.name),
                        'price': current['close'],
                        'momentum': current['momentum'],
                        'signal_strength': current['signal_strength'],
                        'squeeze_duration': 1
                    })
            
            return signals
            
        except Exception as e:
            logger.error(f"Error detecting TTM Squeeze signals: {e}")
            return []

# ============================================================================
# MULTI-TIMEFRAME ANALYSIS
# ============================================================================

class MultiTimeframeAnalyzer:
    """Analyze TTM Squeeze across multiple timeframes (5Min and 15Min only)"""
    
    def __init__(self):
        # Focus only on intraday timeframes
        self.timeframes = ['5Min', '15Min']
        self.ttm_indicators = {
            tf: TTMSqueezeIndicator() for tf in self.timeframes
        }
        
        logger.info(f"Multi-timeframe analyzer initialized for: {', '.join(self.timeframes)}")
    
    async def analyze_symbol(self, symbol: str, data_manager) -> Dict[str, Any]:
        """Analyze symbol across multiple timeframes"""
        try:
            results = {
                'symbol': symbol,
                'timeframes': {},
                'signals': [],
                'confirmations': 0,
                'overall_signal': None
            }
            
            # Analyze each timeframe
            for timeframe in self.timeframes:
                tf_result = await self._analyze_timeframe(symbol, timeframe, data_manager)
                results['timeframes'][timeframe] = tf_result
                
                # Count confirmations
                if tf_result and tf_result.get('has_signal'):
                    results['confirmations'] += 1
                    results['signals'].extend(tf_result.get('signals', []))
            
            # Determine overall signal
            if results['confirmations'] >= 2:  # Both timeframes confirm
                results['overall_signal'] = 'strong'
            elif results['confirmations'] == 1:
                results['overall_signal'] = 'moderate'
            else:
                results['overall_signal'] = 'none'
            
            return results
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            return {'symbol': symbol, 'error': str(e)}
    
    async def _analyze_timeframe(self, symbol: str, timeframe: str, data_manager) -> Optional[Dict[str, Any]]:
        """Analyze single timeframe"""
        try:
            # Get historical data
            data = await data_manager.get_historical_data(symbol, timeframe, 100)
            
            if data is None or len(data) < 50:
                return None
            
            # Calculate TTM Squeeze
            ttm_data = self.ttm_indicators[timeframe].calculate(data)
            
            if ttm_data is None:
                return None
            
            # Detect signals
            signals = self.ttm_indicators[timeframe].detect_signals(ttm_data)
            
            # Get current state
            latest = ttm_data.iloc[-1]
            
            result = {
                'timeframe': timeframe,
                'has_signal': len(signals) > 0,
                'signals': signals,
                'current_state': {
                    'is_squeeze': latest['is_squeeze'],
                    'momentum': latest['momentum'],
                    'signal_strength': latest['signal_strength'],
                    'price': latest['close']
                },
                'data_points': len(ttm_data)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol} {timeframe}: {e}")
            return None

# ============================================================================
# SIGNAL FILTERING AND VALIDATION
# ============================================================================

class SignalFilter:
    """Filter and validate trading signals"""
    
    def __init__(self):
        self.min_signal_strength = 0.3
        self.min_confirmations = 1  # At least 1 timeframe (reduced from 2)
        self.max_signals_per_symbol = 3
        
    def filter_signals(self, analysis_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter signals based on quality criteria"""
        try:
            filtered_signals = []
            
            for result in analysis_results:
                if result.get('error'):
                    continue
                
                symbol = result['symbol']
                confirmations = result['confirmations']
                signals = result.get('signals', [])
                
                # Filter by confirmation count
                if confirmations < self.min_confirmations:
                    continue
                
                # Filter by signal strength
                quality_signals = []
                for signal in signals:
                    if signal.get('signal_strength', 0) >= self.min_signal_strength:
                        quality_signals.append(signal)
                
                # Limit signals per symbol
                quality_signals = quality_signals[:self.max_signals_per_symbol]
                
                if quality_signals:
                    filtered_result = {
                        'symbol': symbol,
                        'signals': quality_signals,
                        'confirmations': confirmations,
                        'timeframes': list(result['timeframes'].keys()),
                        'overall_signal': result['overall_signal'],
                        'timestamp': pd.Timestamp.now()
                    }
                    
                    filtered_signals.append(filtered_result)
            
            # Sort by confirmation count and signal strength
            filtered_signals.sort(
                key=lambda x: (x['confirmations'], max(s.get('signal_strength', 0) for s in x['signals'])),
                reverse=True
            )
            
            logger.info(f"Filtered {len(filtered_signals)} quality signals from {len(analysis_results)} analyzed symbols")
            
            return filtered_signals
            
        except Exception as e:
            logger.error(f"Error filtering signals: {e}")
            return []
    
    def validate_signal(self, signal: Dict[str, Any]) -> bool:
        """Validate individual signal"""
        try:
            # Check required fields
            required_fields = ['symbol', 'signal_type', 'timestamp', 'price']
            for field in required_fields:
                if field not in signal:
                    return False
            
            # Check signal strength
            if signal.get('signal_strength', 0) < self.min_signal_strength:
                return False
            
            # Check price validity
            price = signal.get('price', 0)
            if price <= 0 or price > 10000:  # Reasonable price range
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating signal: {e}")
            return False
